import React, { useRef, useState } from 'react';
import { useLoadingAnimation } from '../hooks/useLoadingAnimation';

// Example 1: Basic usage with default options
export const BasicLoadingButton: React.FC = () => {
  const containerRef = useRef<HTMLElement>(null);
  const textRef = useRef<HTMLElement>(null);
  const iconRef = useRef<HTMLElement>(null);
  
  const [isLoading, setIsLoading] = useState(false);
  
  // Use hook with default options
  const { startLoading, goBack, cleanup } = useLoadingAnimation();
  
  React.useEffect(() => {
    return cleanup;
  }, [cleanup]);
  
  const handleClick = () => {
    if (!isLoading) {
      setIsLoading(true);
      startLoading({ containerRef, textRef, iconRef });
      
      // Simulate async operation
      setTimeout(() => {
        setIsLoading(false);
        goBack({ containerRef, textRef, iconRef });
      }, 3000);
    }
  };
  
  return (
    <button
      ref={containerRef as React.RefObject<HTMLButtonElement>}
      onClick={handleClick}
      disabled={isLoading}
      style={{
        padding: '12px 24px',
        borderRadius: '6px',
        border: '2px solid #007bff',
        backgroundColor: '#007bff',
        color: 'white',
        cursor: isLoading ? 'not-allowed' : 'pointer',
        position: 'relative',
        minWidth: '120px',
      }}
    >
      <span ref={textRef as React.RefObject<HTMLSpanElement>}>
        {isLoading ? 'Loading...' : 'Click Me'}
      </span>
      <div
        ref={iconRef as React.RefObject<HTMLDivElement>}
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%) scale(0)',
          opacity: 0,
        }}
      >
        ⚡
      </div>
    </button>
  );
};

// Example 2: Custom animation options
export const CustomLoadingButton: React.FC = () => {
  const containerRef = useRef<HTMLElement>(null);
  const textRef = useRef<HTMLElement>(null);
  const iconRef = useRef<HTMLElement>(null);
  
  const [isLoading, setIsLoading] = useState(false);
  
  // Use hook with custom options
  const { startLoading, goBack, cleanup } = useLoadingAnimation({
    shapeDuration: 800,
    backgroundDuration: 500,
    iconDuration: 400,
    spinDuration: 1500,
    easing: 'easeOutBack',
    borderColor: '#28a745',
    circleSize: 60,
  });
  
  React.useEffect(() => {
    return cleanup;
  }, [cleanup]);
  
  const handleClick = () => {
    if (!isLoading) {
      setIsLoading(true);
      startLoading({ containerRef, textRef, iconRef });
      
      // Simulate async operation
      setTimeout(() => {
        setIsLoading(false);
        goBack({ containerRef, textRef, iconRef });
      }, 4000);
    }
  };
  
  return (
    <button
      ref={containerRef as React.RefObject<HTMLButtonElement>}
      onClick={handleClick}
      disabled={isLoading}
      style={{
        padding: '16px 32px',
        borderRadius: '8px',
        border: '2px solid #28a745',
        backgroundColor: '#28a745',
        color: 'white',
        cursor: isLoading ? 'not-allowed' : 'pointer',
        position: 'relative',
        minWidth: '150px',
        fontSize: '16px',
        fontWeight: 'bold',
      }}
    >
      <span ref={textRef as React.RefObject<HTMLSpanElement>}>
        Save Changes
      </span>
      <div
        ref={iconRef as React.RefObject<HTMLDivElement>}
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%) scale(0)',
          opacity: 0,
          fontSize: '20px',
        }}
      >
        ✓
      </div>
    </button>
  );
};

// Example 3: Manual control (you control when to start/stop)
export const ManualControlButton: React.FC = () => {
  const containerRef = useRef<HTMLElement>(null);
  const textRef = useRef<HTMLElement>(null);
  const iconRef = useRef<HTMLElement>(null);
  
  const [isLoading, setIsLoading] = useState(false);
  
  const { startLoading, goBack, cleanup } = useLoadingAnimation({
    borderColor: '#dc3545',
    circleSize: 45,
    spinDuration: 800,
  });
  
  React.useEffect(() => {
    return cleanup;
  }, [cleanup]);
  
  const handleStart = () => {
    setIsLoading(true);
    startLoading({ containerRef, textRef, iconRef });
  };
  
  const handleStop = () => {
    setIsLoading(false);
    goBack({ containerRef, textRef, iconRef });
  };
  
  return (
    <div style={{ display: 'flex', gap: '10px', alignItems: 'center' }}>
      <button
        ref={containerRef as React.RefObject<HTMLButtonElement>}
        style={{
          padding: '12px 24px',
          borderRadius: '6px',
          border: '2px solid #dc3545',
          backgroundColor: '#dc3545',
          color: 'white',
          position: 'relative',
          minWidth: '120px',
        }}
      >
        <span ref={textRef as React.RefObject<HTMLSpanElement>}>
          Processing
        </span>
        <div
          ref={iconRef as React.RefObject<HTMLDivElement>}
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%) scale(0)',
            opacity: 0,
          }}
        >
          🔄
        </div>
      </button>
      
      <button onClick={handleStart} disabled={isLoading}>
        Start
      </button>
      <button onClick={handleStop} disabled={!isLoading}>
        Stop
      </button>
    </div>
  );
};

// Example 4: Integration with async function
export const AsyncFunctionButton: React.FC = () => {
  const containerRef = useRef<HTMLElement>(null);
  const textRef = useRef<HTMLElement>(null);
  const iconRef = useRef<HTMLElement>(null);
  
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<string>('');
  
  const { startLoading, goBack, cleanup } = useLoadingAnimation({
    borderColor: '#6f42c1',
    easing: 'easeOutElastic',
  });
  
  React.useEffect(() => {
    return cleanup;
  }, [cleanup]);
  
  // Simulate an async API call
  const fetchData = async (): Promise<string> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve('Data loaded successfully!');
      }, 2500);
    });
  };
  
  const handleClick = async () => {
    if (!isLoading) {
      setIsLoading(true);
      setResult('');
      startLoading({ containerRef, textRef, iconRef });
      
      try {
        const data = await fetchData();
        setResult(data);
      } catch (error) {
        setResult('Error loading data');
      } finally {
        setIsLoading(false);
        goBack({ containerRef, textRef, iconRef });
      }
    }
  };
  
  return (
    <div>
      <button
        ref={containerRef as React.RefObject<HTMLButtonElement>}
        onClick={handleClick}
        disabled={isLoading}
        style={{
          padding: '12px 24px',
          borderRadius: '6px',
          border: '2px solid #6f42c1',
          backgroundColor: '#6f42c1',
          color: 'white',
          cursor: isLoading ? 'not-allowed' : 'pointer',
          position: 'relative',
          minWidth: '140px',
        }}
      >
        <span ref={textRef as React.RefObject<HTMLSpanElement>}>
          Fetch Data
        </span>
        <div
          ref={iconRef as React.RefObject<HTMLDivElement>}
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%) scale(0)',
            opacity: 0,
          }}
        >
          📡
        </div>
      </button>
      {result && (
        <p style={{ marginTop: '10px', color: '#6f42c1' }}>
          {result}
        </p>
      )}
    </div>
  );
};
