import { useState } from "react";
import reactLogo from "./assets/react.svg";
import viteLogo from "/vite.svg";
import "./App.scss";
import { Button } from "./stories/Button/Button";
import { LoadingButtonDemo } from "./components/LoadingButtonDemo";

function App() {
  const [count, setCount] = useState(0);
  const [showDemo, setShowDemo] = useState(false);

  return (
    <>
      <div>
        <a href="https://vite.dev" target="_blank">
          <img src={viteLogo} className="logo" alt="Vite logo" />
        </a>
        <a href="https://react.dev" target="_blank">
          <img src={reactLogo} className="logo react" alt="React logo" />
        </a>
      </div>
      <h1>React Kino UI</h1>
      <div className="card">
        <button onClick={() => setCount((count) => count + 1)}>count is {count}</button>
        <p>
          Edit <code>src/App.tsx</code> and save to test HMR
        </p>
        <div style={{ margin: "2rem 0", display: "flex", gap: "1rem", flexWrap: "wrap", justifyContent: "center" }}>
          <Button primary label="Original Button" />
          <button
            onClick={() => setShowDemo(!showDemo)}
            style={{
              padding: "12px 24px",
              backgroundColor: "#059669",
              color: "white",
              border: "none",
              borderRadius: "8px",
              cursor: "pointer",
              fontWeight: "600",
            }}
          >
            {showDemo ? "Hide" : "Show"} Loading Animation Demo
          </button>
        </div>
      </div>

      {showDemo && <LoadingButtonDemo />}

      <p className="read-the-docs">Click on the Vite and React logos to learn more</p>
    </>
  );
}

export default App;
