.loading-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  font-family: 'Nunito Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-weight: 600;
  line-height: 1;
  text-decoration: none;
  transition: all 0.2s ease;
  overflow: hidden;
  
  &:disabled {
    cursor: not-allowed;
  }

  &:focus {
    outline: 2px solid #4f46e5;
    outline-offset: 2px;
  }

  // Variants
  &--primary {
    background-color: #4f46e5;
    color: white;
    border-radius: 8px;
    
    &:hover:not(:disabled) {
      background-color: #4338ca;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
    }
    
    &:active:not(:disabled) {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(79, 70, 229, 0.3);
    }
  }

  &--secondary {
    background-color: transparent;
    color: #374151;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    
    &:hover:not(:disabled) {
      border-color: #9ca3af;
      background-color: #f9fafb;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    &:active:not(:disabled) {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  // Sizes
  &--small {
    padding: 8px 16px;
    font-size: 14px;
    min-height: 36px;
    min-width: 80px;
  }

  &--medium {
    padding: 12px 24px;
    font-size: 16px;
    min-height: 44px;
    min-width: 100px;
  }

  &--large {
    padding: 16px 32px;
    font-size: 18px;
    min-height: 52px;
    min-width: 120px;
  }

  // Text element
  &__text {
    display: block;
    white-space: nowrap;
  }

  // Icon element
  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    
    svg {
      display: block;
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .loading-button {
    &--secondary {
      color: #f3f4f6;
      border-color: #4b5563;
      
      &:hover:not(:disabled) {
        border-color: #6b7280;
        background-color: #1f2937;
      }
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .loading-button {
    transition: none;
    
    &:hover:not(:disabled) {
      transform: none;
    }
    
    &:active:not(:disabled) {
      transform: none;
    }
  }
}
