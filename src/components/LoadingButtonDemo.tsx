import { useState } from 'react';
import { LoadingButton, DownloadIcon, CheckIcon } from './LoadingButton';
import './LoadingButton.scss';

// Additional Heroicons examples
const SaveIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z" />
    <polyline points="17,21 17,13 7,13 7,21" />
    <polyline points="7,3 7,8 15,8" />
  </svg>
);

const UploadIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-15" />
    <polyline points="17,8 12,3 7,8" />
    <line x1="12" y1="3" x2="12" y2="15" />
  </svg>
);

const RefreshIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <polyline points="23,4 23,10 17,10" />
    <polyline points="1,20 1,14 7,14" />
    <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" />
  </svg>
);

export const LoadingButtonDemo: React.FC = () => {
  const [loadingStates, setLoadingStates] = useState({
    download: false,
    save: false,
    upload: false,
    refresh: false,
  });

  const toggleLoading = (buttonType: keyof typeof loadingStates) => {
    setLoadingStates(prev => ({
      ...prev,
      [buttonType]: !prev[buttonType],
    }));
  };

  const simulateAsyncAction = (buttonType: keyof typeof loadingStates) => {
    setLoadingStates(prev => ({ ...prev, [buttonType]: true }));
    
    // Simulate async operation
    setTimeout(() => {
      setLoadingStates(prev => ({ ...prev, [buttonType]: false }));
    }, 3000);
  };

  return (
    <div style={{ padding: '2rem', maxWidth: '800px', margin: '0 auto' }}>
      <h1>Loading Button Animation Demo</h1>
      <p>Click buttons to see the 4-step animation sequence:</p>
      <ol>
        <li>Shape transforms to circle</li>
        <li>Background becomes transparent with 3/4 border</li>
        <li>Icon appears in center</li>
        <li>Starts spinning</li>
      </ol>

      <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem', marginTop: '2rem' }}>
        
        {/* Manual Toggle Section */}
        <section>
          <h2>Manual Toggle (Click to start/stop)</h2>
          <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
            <LoadingButton
              label="Download"
              isLoading={loadingStates.download}
              onClick={() => toggleLoading('download')}
              icon={<DownloadIcon />}
              variant="primary"
              animationOptions={{
                borderColor: '#4f46e5',
                circleSize: 48,
              }}
            />
            
            <LoadingButton
              label="Save Document"
              isLoading={loadingStates.save}
              onClick={() => toggleLoading('save')}
              icon={<SaveIcon />}
              variant="secondary"
              animationOptions={{
                borderColor: '#059669',
                circleSize: 50,
                shapeDuration: 800,
              }}
            />
            
            <LoadingButton
              label="Upload"
              isLoading={loadingStates.upload}
              onClick={() => toggleLoading('upload')}
              icon={<UploadIcon />}
              variant="primary"
              size="large"
              animationOptions={{
                borderColor: '#dc2626',
                circleSize: 56,
                spinDuration: 800,
              }}
            />
          </div>
        </section>

        {/* Simulated Async Section */}
        <section>
          <h2>Simulated Async Operations (3 second duration)</h2>
          <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
            <LoadingButton
              label="Refresh Data"
              isLoading={loadingStates.refresh}
              onClick={() => simulateAsyncAction('refresh')}
              icon={<RefreshIcon />}
              variant="secondary"
              size="small"
              animationOptions={{
                borderColor: '#7c3aed',
                circleSize: 40,
                easing: 'easeOutBack',
              }}
            />
            
            <LoadingButton
              label="Process Payment"
              isLoading={false}
              onClick={() => simulateAsyncAction('download')}
              icon={<CheckIcon />}
              variant="primary"
              size="medium"
              animationOptions={{
                borderColor: '#059669',
                shapeDuration: 400,
                backgroundDuration: 300,
                iconDuration: 200,
                spinDuration: 1200,
              }}
            />
          </div>
        </section>

        {/* Different Configurations */}
        <section>
          <h2>Different Animation Configurations</h2>
          <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
            <LoadingButton
              label="Fast Animation"
              isLoading={false}
              onClick={() => toggleLoading('download')}
              icon={<DownloadIcon />}
              variant="primary"
              animationOptions={{
                shapeDuration: 300,
                backgroundDuration: 200,
                iconDuration: 150,
                spinDuration: 600,
                borderColor: '#f59e0b',
              }}
            />
            
            <LoadingButton
              label="Slow & Smooth"
              isLoading={false}
              onClick={() => toggleLoading('save')}
              icon={<SaveIcon />}
              variant="secondary"
              animationOptions={{
                shapeDuration: 1000,
                backgroundDuration: 600,
                iconDuration: 400,
                spinDuration: 1500,
                easing: 'easeOutElastic(1, .8)',
                borderColor: '#8b5cf6',
              }}
            />
          </div>
        </section>
      </div>

      <div style={{ marginTop: '3rem', padding: '1rem', backgroundColor: '#f3f4f6', borderRadius: '8px' }}>
        <h3>Usage Example:</h3>
        <pre style={{ fontSize: '14px', overflow: 'auto' }}>
{`import { LoadingButton, DownloadIcon } from './LoadingButton';

<LoadingButton
  label="Download File"
  isLoading={isDownloading}
  onClick={handleDownload}
  icon={<DownloadIcon />}
  variant="primary"
  animationOptions={{
    borderColor: '#4f46e5',
    circleSize: 48,
    spinDuration: 1000,
  }}
/>`}
        </pre>
      </div>
    </div>
  );
};
