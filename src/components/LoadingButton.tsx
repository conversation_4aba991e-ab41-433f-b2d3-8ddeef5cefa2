import { useRef, useEffect } from 'react';
import { useLoadingAnimation, LoadingAnimationOptions } from '../hooks/useLoadingAnimation';

export interface LoadingButtonProps {
  /** Button label */
  label: string;
  /** Is button in loading state */
  isLoading?: boolean;
  /** Click handler */
  onClick?: () => void;
  /** Animation configuration */
  animationOptions?: LoadingAnimationOptions;
  /** Custom icon component (SVG) */
  icon?: React.ReactNode;
  /** Button variant */
  variant?: 'primary' | 'secondary';
  /** Button size */
  size?: 'small' | 'medium' | 'large';
  /** Custom className */
  className?: string;
}

// Default Heroicons spinner icon (you can replace with any Heroicons SVG)
const DefaultSpinnerIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M21 12a9 9 0 11-6.219-8.56" />
  </svg>
);

// Example with Heroicons download icon
export const DownloadIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
    <polyline points="7,10 12,15 17,10" />
    <line x1="12" y1="15" x2="12" y2="3" />
  </svg>
);

// Example with Heroicons check icon
export const CheckIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <polyline points="20,6 9,17 4,12" />
  </svg>
);

export const LoadingButton: React.FC<LoadingButtonProps> = ({
  label,
  isLoading = false,
  onClick,
  animationOptions,
  icon = <DefaultSpinnerIcon />,
  variant = 'primary',
  size = 'medium',
  className = '',
}) => {
  const containerRef = useRef<HTMLButtonElement>(null);
  const textRef = useRef<HTMLSpanElement>(null);
  const iconRef = useRef<HTMLDivElement>(null);

  const { startLoading, goBack, cleanup } = useLoadingAnimation(animationOptions);

  useEffect(() => {
    if (isLoading) {
      startLoading({ containerRef, textRef, iconRef });
    } else {
      goBack({ containerRef, textRef, iconRef });
    }
  }, [isLoading, startLoading, goBack]);

  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  const baseClasses = [
    'loading-button',
    `loading-button--${variant}`,
    `loading-button--${size}`,
    className,
  ].filter(Boolean).join(' ');

  return (
    <button
      ref={containerRef}
      onClick={onClick}
      disabled={isLoading}
      className={baseClasses}
      type="button"
    >
      <span ref={textRef} className="loading-button__text">
        {label}
      </span>
      <div
        ref={iconRef}
        className="loading-button__icon"
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%) scale(0)',
          opacity: 0,
        }}
      >
        {icon}
      </div>
    </button>
  );
};
