import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { LoadingButton } from './LoadingButton';

const meta = {
  title: 'Components/LoadingButton',
  component: LoadingButton,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: `
A sophisticated loading button with smooth 4-step animation sequence:
1. <PERSON><PERSON><PERSON> transforms to circle
2. Background becomes transparent with 3/4 border
3. Icon appears in center  
4. Starts spinning

Built with Anime.js for complex timeline control and smooth transitions.
        `,
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['primary', 'secondary'],
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium', 'large'],
    },
    isLoading: {
      control: { type: 'boolean' },
    },
    animationOptions: {
      control: { type: 'object' },
    },
  },
  args: { 
    onClick: fn(),
  },
} satisfies Meta<typeof LoadingButton>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    label: 'Download File',
    variant: 'primary',
    size: 'medium',
    isLoading: false,
  },
};

export const Secondary: Story = {
  args: {
    label: 'Save Document',
    variant: 'secondary',
    size: 'medium',
    isLoading: false,
  },
};

export const Loading: Story = {
  args: {
    label: 'Processing...',
    variant: 'primary',
    size: 'medium',
    isLoading: true,
  },
};

export const Small: Story = {
  args: {
    label: 'Upload',
    variant: 'primary',
    size: 'small',
    isLoading: false,
  },
};

export const Large: Story = {
  args: {
    label: 'Submit Form',
    variant: 'secondary',
    size: 'large',
    isLoading: false,
  },
};

export const CustomAnimation: Story = {
  args: {
    label: 'Custom Animation',
    variant: 'primary',
    size: 'medium',
    isLoading: false,
    animationOptions: {
      shapeDuration: 800,
      backgroundDuration: 500,
      iconDuration: 300,
      spinDuration: 1200,
      easing: 'easeOutElastic(1, .8)',
      borderColor: '#8b5cf6',
      circleSize: 60,
    },
  },
};

export const FastAnimation: Story = {
  args: {
    label: 'Fast Loading',
    variant: 'secondary',
    size: 'medium',
    isLoading: false,
    animationOptions: {
      shapeDuration: 300,
      backgroundDuration: 200,
      iconDuration: 150,
      spinDuration: 600,
      borderColor: '#f59e0b',
      circleSize: 40,
    },
  },
};
