import { useRef, useCallback } from 'react';
import anime from 'animejs';

export interface LoadingAnimationOptions {
  /** Duration for shape transformation (ms) */
  shapeDuration?: number;
  /** Duration for background animation (ms) */
  backgroundDuration?: number;
  /** Duration for icon appearance (ms) */
  iconDuration?: number;
  /** Duration for spin animation (ms) */
  spinDuration?: number;
  /** Easing function */
  easing?: string;
  /** Border color */
  borderColor?: string;
  /** Size of the circular state */
  circleSize?: number;
}

export interface LoadingAnimationRefs {
  containerRef: React.RefObject<HTMLElement>;
  textRef: React.RefObject<HTMLElement>;
  iconRef: React.RefObject<HTMLElement>;
}

const defaultOptions: Required<LoadingAnimationOptions> = {
  shapeDuration: 600,
  backgroundDuration: 400,
  iconDuration: 300,
  spinDuration: 1000,
  easing: 'easeOutCubic',
  borderColor: '#000000',
  circleSize: 50,
};

export const useLoadingAnimation = (options: LoadingAnimationOptions = {}) => {
  const config = { ...defaultOptions, ...options };
  const timelineRef = useRef<anime.AnimeTimelineInstance | null>(null);
  const spinAnimationRef = useRef<anime.AnimeInstance | null>(null);
  const originalStylesRef = useRef<{
    width: string;
    height: string;
    borderRadius: string;
    backgroundColor: string;
    border: string;
  } | null>(null);

  const saveOriginalStyles = useCallback((element: HTMLElement) => {
    const computedStyle = window.getComputedStyle(element);
    originalStylesRef.current = {
      width: computedStyle.width,
      height: computedStyle.height,
      borderRadius: computedStyle.borderRadius,
      backgroundColor: computedStyle.backgroundColor,
      border: computedStyle.border,
    };
  }, []);

  const startLoading = useCallback(
    ({ containerRef, textRef, iconRef }: LoadingAnimationRefs) => {
      const container = containerRef.current;
      const text = textRef.current;
      const icon = iconRef.current;

      if (!container || !text || !icon) {
        console.warn('useLoadingAnimation: Missing required refs');
        return;
      }

      // Save original styles if not already saved
      if (!originalStylesRef.current) {
        saveOriginalStyles(container);
      }

      // Stop any existing animations
      if (timelineRef.current) {
        timelineRef.current.pause();
      }
      if (spinAnimationRef.current) {
        spinAnimationRef.current.pause();
      }

      // Create the main timeline
      timelineRef.current = anime.timeline({
        easing: config.easing,
        autoplay: true,
      });

      // Step 1: Hide text and transform to circle
      timelineRef.current
        .add({
          targets: text,
          opacity: 0,
          duration: config.shapeDuration * 0.3,
        })
        .add({
          targets: container,
          width: config.circleSize,
          height: config.circleSize,
          borderRadius: '50%',
          duration: config.shapeDuration,
        }, `-=${config.shapeDuration * 0.2}`)
        
        // Step 2: Animate background and create 3/4 border
        .add({
          targets: container,
          backgroundColor: 'transparent',
          borderTop: `2px solid ${config.borderColor}`,
          borderRight: `2px solid ${config.borderColor}`,
          borderLeft: `2px solid ${config.borderColor}`,
          borderBottom: 'none',
          duration: config.backgroundDuration,
        })
        
        // Step 3: Show icon
        .add({
          targets: icon,
          opacity: 1,
          scale: [0, 1],
          duration: config.iconDuration,
        }, `-=${config.backgroundDuration * 0.3}`)
        
        // Step 4: Start spinning (this will be handled separately for infinite loop)
        .add({
          targets: container,
          rotate: '0deg', // Reset rotation before starting spin
          duration: 1,
          complete: () => {
            // Start infinite spin animation
            spinAnimationRef.current = anime({
              targets: container,
              rotate: '360deg',
              duration: config.spinDuration,
              loop: true,
              easing: 'linear',
            });
          }
        });
    },
    [config, saveOriginalStyles]
  );

  const goBack = useCallback(
    ({ containerRef, textRef, iconRef }: LoadingAnimationRefs) => {
      const container = containerRef.current;
      const text = textRef.current;
      const icon = iconRef.current;

      if (!container || !text || !icon || !originalStylesRef.current) {
        console.warn('useLoadingAnimation: Missing required refs or original styles');
        return;
      }

      // Stop existing animations
      if (timelineRef.current) {
        timelineRef.current.pause();
      }
      if (spinAnimationRef.current) {
        spinAnimationRef.current.pause();
      }

      const originalStyles = originalStylesRef.current;

      // Create reverse timeline
      timelineRef.current = anime.timeline({
        easing: config.easing,
        autoplay: true,
      });

      // Step 4 to 3: Stop spinning and hide icon
      timelineRef.current
        .add({
          targets: container,
          rotate: '0deg',
          duration: config.spinDuration * 0.3,
          easing: 'easeOutQuart',
        })
        .add({
          targets: icon,
          opacity: 0,
          scale: 0,
          duration: config.iconDuration,
        }, `-=${config.spinDuration * 0.1}`)
        
        // Step 2 to 1: Restore background and border
        .add({
          targets: container,
          backgroundColor: originalStyles.backgroundColor,
          border: originalStyles.border,
          duration: config.backgroundDuration,
        })
        
        // Step 1: Transform back to original shape and show text
        .add({
          targets: container,
          width: originalStyles.width,
          height: originalStyles.height,
          borderRadius: originalStyles.borderRadius,
          duration: config.shapeDuration,
        }, `-=${config.backgroundDuration * 0.3}`)
        .add({
          targets: text,
          opacity: 1,
          duration: config.shapeDuration * 0.4,
        }, `-=${config.shapeDuration * 0.2}`);
    },
    [config]
  );

  const cleanup = useCallback(() => {
    if (timelineRef.current) {
      timelineRef.current.pause();
      timelineRef.current = null;
    }
    if (spinAnimationRef.current) {
      spinAnimationRef.current.pause();
      spinAnimationRef.current = null;
    }
  }, []);

  return {
    startLoading,
    goBack,
    cleanup,
  };
};
