import { useRef, useState, useEffect } from "react";
import "./button.scss";
import DownloadIcon from "../../icons/DownloadIcon";
import { useLoadingAnimation } from "../../hooks/useLoadingAnimation";

export interface ButtonProps {
  /** Is this the principal call to action on the page? */
  primary?: boolean;
  /** What background color to use */
  backgroundColor?: string;
  /** How large should the button be? */
  size?: "small" | "medium" | "large";
  /** Button contents */
  label: string;
  /** Optional click handler */
  onClick?: () => void;
}

export const Button = ({ primary = false, size = "medium", backgroundColor, label, onClick, ...props }: ButtonProps) => {
  const buttonRef = useRef<HTMLElement>(null);
  const textRef = useRef<HTMLElement>(null);
  const iconRef = useRef<HTMLElement>(null);

  const [isLoading, setIsLoading] = useState(false);

  const mode = primary ? "storybook-button--primary" : "storybook-button--secondary";

  // Use the loading animation hook with custom options
  const { startLoading, goBack, cleanup } = useLoadingAnimation({
    shapeDuration: 600,
    backgroundDuration: 400,
    iconDuration: 300,
    spinDuration: 1000,
    easing: "easeOutCubic",
    borderColor: primary ? "#ffffff" : "#000000",
    circleSize: 50,
  });

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  const handleClick = () => {
    if (!isLoading) {
      setIsLoading(true);
      startLoading({ containerRef: buttonRef, textRef, iconRef });

      // Auto transform back after 2 seconds (simulating async operation)
      setTimeout(() => {
        setIsLoading(false);
        goBack({ containerRef: buttonRef, textRef, iconRef });
      }, 2000);
    }

    if (onClick) {
      onClick();
    }
  };

  return (
    <button
      ref={buttonRef as React.RefObject<HTMLButtonElement>}
      onClick={handleClick}
      type="button"
      className={["kino-button", `kino-button--${size}`, mode].join(" ")}
      style={{ backgroundColor }}
      {...props}
    >
      <span ref={textRef as React.RefObject<HTMLSpanElement>} style={{ display: "initial" }}>
        {label}
      </span>
      <div ref={iconRef as React.RefObject<HTMLDivElement>} style={{ position: "absolute", display: "none", transform: "scale(0)" }}>
        <DownloadIcon />
      </div>
    </button>
  );
};
