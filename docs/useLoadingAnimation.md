# useLoadingAnimation Hook

A React hook that provides smooth loading animations for buttons and other interactive elements. It transforms elements from their normal state to a circular loading spinner with customizable animations.

## Features

- 🎯 **Smooth Transitions**: Seamless morphing from button to circular loader
- ⚙️ **Highly Customizable**: Control timing, colors, sizes, and easing functions
- 🔄 **Reversible**: Smooth transition back to original state
- 🎨 **Visual Effects**: Includes text fade, shape transformation, and spinning animation
- 📱 **Responsive**: Works with any button size and adapts accordingly
- 🧹 **Memory Safe**: Automatic cleanup to prevent memory leaks

## Installation

The hook is already included in this project. Import it from:

```typescript
import { useLoadingAnimation } from '../hooks/useLoadingAnimation';
```

## Basic Usage

```typescript
import React, { useRef, useState } from 'react';
import { useLoadingAnimation } from '../hooks/useLoadingAnimation';

const MyButton = () => {
  const containerRef = useRef<HTMLElement>(null);
  const textRef = useRef<HTMLElement>(null);
  const iconRef = useRef<HTMLElement>(null);
  
  const [isLoading, setIsLoading] = useState(false);
  
  // Initialize the hook
  const { startLoading, goBack, cleanup } = useLoadingAnimation();
  
  // Cleanup on unmount
  React.useEffect(() => {
    return cleanup;
  }, [cleanup]);
  
  const handleClick = async () => {
    if (!isLoading) {
      setIsLoading(true);
      startLoading({ containerRef, textRef, iconRef });
      
      // Your async operation here
      await someAsyncOperation();
      
      setIsLoading(false);
      goBack({ containerRef, textRef, iconRef });
    }
  };
  
  return (
    <button
      ref={containerRef as React.RefObject<HTMLButtonElement>}
      onClick={handleClick}
      disabled={isLoading}
    >
      <span ref={textRef as React.RefObject<HTMLSpanElement>}>
        Click Me
      </span>
      <div
        ref={iconRef as React.RefObject<HTMLDivElement>}
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%) scale(0)',
          opacity: 0,
        }}
      >
        ⚡ {/* Your loading icon */}
      </div>
    </button>
  );
};
```

## API Reference

### Hook Signature

```typescript
const { startLoading, goBack, cleanup } = useLoadingAnimation(options?);
```

### Options

```typescript
interface LoadingAnimationOptions {
  /** Duration for shape transformation (ms) */
  shapeDuration?: number;        // default: 600
  /** Duration for background animation (ms) */
  backgroundDuration?: number;   // default: 400
  /** Duration for icon appearance (ms) */
  iconDuration?: number;         // default: 300
  /** Duration for spin animation (ms) */
  spinDuration?: number;         // default: 1000
  /** Easing function */
  easing?: string;               // default: "easeOutCubic"
  /** Border color */
  borderColor?: string;          // default: "#000000"
  /** Size of the circular state */
  circleSize?: number;           // default: 50
}
```

### Return Values

```typescript
interface ReturnValues {
  startLoading: (refs: LoadingAnimationRefs) => void;
  goBack: (refs: LoadingAnimationRefs) => void;
  cleanup: () => void;
}

interface LoadingAnimationRefs {
  containerRef: React.RefObject<HTMLElement | null>;
  textRef: React.RefObject<HTMLElement | null>;
  iconRef: React.RefObject<HTMLElement | null>;
}
```

## Animation Sequence

### Forward Animation (startLoading)
1. **Text Fade**: Text opacity fades to 0
2. **Shape Transform**: Container morphs to circular shape
3. **Background Change**: Background becomes transparent with 3/4 border
4. **Icon Appear**: Loading icon scales in and becomes visible
5. **Spin Start**: Infinite rotation animation begins

### Reverse Animation (goBack)
1. **Spin Stop**: Rotation stops and resets to 0°
2. **Icon Hide**: Loading icon scales out and fades
3. **Background Restore**: Original background and border restored
4. **Shape Restore**: Container returns to original dimensions
5. **Text Show**: Text fades back in

## Examples

### Custom Styling
```typescript
const { startLoading, goBack, cleanup } = useLoadingAnimation({
  shapeDuration: 800,
  backgroundDuration: 500,
  iconDuration: 400,
  spinDuration: 1500,
  easing: 'easeOutBack',
  borderColor: '#007bff',
  circleSize: 60,
});
```

### With Async/Await
```typescript
const handleSubmit = async () => {
  setIsLoading(true);
  startLoading({ containerRef, textRef, iconRef });
  
  try {
    await submitForm();
    // Success handling
  } catch (error) {
    // Error handling
  } finally {
    setIsLoading(false);
    goBack({ containerRef, textRef, iconRef });
  }
};
```

## Required HTML Structure

Your component must have this structure:

```jsx
<button ref={containerRef}>
  <span ref={textRef}>Button Text</span>
  <div ref={iconRef} style={{
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%) scale(0)',
    opacity: 0,
  }}>
    {/* Your loading icon/spinner */}
  </div>
</button>
```

## Important Notes

1. **Container Position**: The container should have `position: relative` for proper icon positioning
2. **Icon Styling**: The icon element must be absolutely positioned and initially hidden
3. **Cleanup**: Always call the cleanup function in useEffect to prevent memory leaks
4. **Refs**: All three refs (container, text, icon) are required
5. **State Management**: Manage loading state separately to control button disabled state

## Easing Options

Available easing functions:
- `"linear"`
- `"easeOutCubic"` (default)
- `"easeOutBack"`
- `"easeOutElastic"`
- `"easeOutBounce"`
- And more from animejs

## Browser Support

This hook uses animejs v4, which supports:
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Performance

The hook is optimized for performance:
- Uses `useMemo` to prevent unnecessary re-renders
- Automatic cleanup prevents memory leaks
- Efficient animation timeline management
- Minimal DOM manipulations
