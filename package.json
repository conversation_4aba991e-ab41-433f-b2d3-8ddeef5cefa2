{"name": "react-kino-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"animejs": "~4.1.3", "react": "~19.1.1", "react-dom": "~19.1.1"}, "devDependencies": {"@chromatic-com/storybook": "~4.1.1", "@eslint/js": "~9.33.0", "@storybook/addon-a11y": "~9.1.5", "@storybook/addon-docs": "~9.1.5", "@storybook/addon-onboarding": "~9.1.5", "@storybook/addon-vitest": "~9.1.5", "@storybook/react-vite": "~9.1.5", "@types/animejs": "^3.1.13", "@types/react": "~19.1.10", "@types/react-dom": "~19.1.7", "@vitejs/plugin-react": "~5.0.0", "@vitest/browser": "~3.2.4", "@vitest/coverage-v8": "~3.2.4", "eslint": "~9.33.0", "eslint-plugin-react-hooks": "~5.2.0", "eslint-plugin-react-refresh": "~0.4.20", "eslint-plugin-storybook": "~9.1.5", "globals": "~16.3.0", "playwright": "~1.55.0", "sass": "~1.92.1", "storybook": "~9.1.5", "typescript": "~5.8.3", "typescript-eslint": "~8.39.1", "vite": "~7.1.2", "vitest": "~3.2.4"}}